// src/game/powerups/registry/trees/quantumEntanglementTree.ts

/*

5. Quantum Entanglement Tree
Core Mechanic: Linked effects and quantum resonance
Branch Structure:

Root: "Quantum Resonator" - Mark enemies with quantum signatures
Branch A: "Entangled Fate"

Damage dealt to one marked enemy is partially dealt to all other marked enemies
Upgrades: Higher damage sharing percentage → Marks spread to nearby enemies → Killing marked enemy damages all others


Branch B: "Quantum Echo"

Your actions create delayed quantum echoes that repeat after a short delay
Upgrades: Stronger echo effects → Multiple echoes → Echoes can trigger other powerup effects

*/

