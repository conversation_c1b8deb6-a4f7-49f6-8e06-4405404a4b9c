// src/core/input/interfaces/GamePadButtonAlias.ts

export type GamepadButtonAlias =
  | 'A'           // A button
  | 'X'            // X button
  | 'Y'             // Y button
  | 'B'              // RT (right trigger)
  | 'secondary'         // B button
  | 'leftBumper'        // LB
  | 'rightBumper'       // RB
  | 'leftTrigger'       // LT
  | 'rightTrigger'      // RT (right trigger)
  | 'select'            // Back/View button
  | 'start'             // Start/Menu button
  | 'leftStickButton'   // Left stick click (L3)
  | 'rightStickButton'  // Right stick click (R3)
  | 'dpadUp'            // D-pad Up
  | 'dpadDown'          // D-pad Down
  | 'dpadLeft'          // D-pad Left
  | 'dpadRight'         // D-pad Right
  | 'home';             // Xbox/Guide button