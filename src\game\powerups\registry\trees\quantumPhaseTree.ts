// src/game/powerups/registry/trees/quantumPhaseTree.ts

/*
Enters phasing on afterburner use

1. Quantum Phase Tree
Core Mechanic: Teleportation and phasing abilities
Branch Structure:

Root: "Quantum Drive" - Unlocks quantum teleportation mechanics
Branch A: "Phase Walker"

Gain ability to briefly phase through enemies and projectiles (0.5s cooldown)
Upgrades: Longer phase duration → Phase damages enemies you pass through → Phase recharges shields


Branch B: "Quantum Leap"
Replaces afterburner use with quantum leap (afterburner explosion still applies)
Gain short-range teleport dash ability (3s cooldown)
Upgrades: Longer teleport range → Teleport creates damaging rift → Multiple teleport charges

*/

