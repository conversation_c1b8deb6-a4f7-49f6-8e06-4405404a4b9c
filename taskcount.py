import re

def parse_task_completion(md_path: str):
    with open(md_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Match [ ] (incomplete) and [x] (complete), case-insensitive for [x]
    incomplete_tasks = re.findall(r'\[ \]', content)
    completed_tasks = re.findall(r'\[x\]', content, re.IGNORECASE)

    num_incomplete = len(incomplete_tasks)
    num_completed = len(completed_tasks)
    total_tasks = num_incomplete + num_completed

    print(f"Remaining Tasks: {num_incomplete}")
    print(f"Completed Tasks: {num_completed}")
    print(f"Total Tasks: {total_tasks}")

# Example usage
if __name__ == '__main__':
    parse_task_completion('README.md')
