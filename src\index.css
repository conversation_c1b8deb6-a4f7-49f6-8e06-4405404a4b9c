/* src/index.css */

/* Fullscreen black backdrop */
body, html, #root {
  margin: 0;
  padding: 0;
  background-color: #000000;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Centered container for all canvas layers */
#canvas-root {
  position: relative;
  /* width: 1920px;
  height: 1080px; */
  cursor: none;
}

/* Stack all canvas layers on top of each other */
canvas {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
}
