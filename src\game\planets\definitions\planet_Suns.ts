// src/game/planets/definitions/planet_Suns.ts
import type { PlanetDefinition } from '../interfaces/PlanetDefinition';

export const SmallSun: PlanetDefinition = {
  name: 'SmallSun',
  imagePath: 'assets/planets/suns/1.png',
  scale: 4,
  interactionDialogueId: 'planet-generic',
  // approachDialogueId: 'dialogue-aetherion-approach',
};

export const MediumSun: PlanetDefinition = {
  name: 'MediumSun',
  imagePath: 'assets/planets/suns/1.png',
  scale: 6,
  interactionDialogueId: 'planet-generic',
  // approachDialogueId: 'dialogue-aetherion-approach',
};

export const LargeSun: PlanetDefinition = {
  name: 'LargeSun',
  imagePath: 'assets/planets/suns/1.png',
  scale: 10,
  interactionDialogueId: 'planet-generic',
  // approachDialogueId: 'dialogue-aetherion-approach',
};

export const HugeSun: PlanetDefinition = {
  name: 'HugeSun',
  imagePath: 'assets/planets/suns/1.png',
  scale: 14,
  interactionDialogueId: 'planet-generic',
  // approachDialogueId: 'dialogue-aetherion-approach',
};
