{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "Node", "allowJs": true, "checkJs": false, "outDir": "dist", "rootDir": "src", "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true, "baseUrl": ".", "jsx": "react-jsx", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "public"]}