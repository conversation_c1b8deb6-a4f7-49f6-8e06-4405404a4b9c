// src/scenes/ship_selection/components/EquippedArtifactsComponent.ts

export class EquippedArtifactsComponent {
  constructor() {
    // Will eventually receive list of equipped artifacts (stub for now)
  }

  update(): void {
    // Handle input when equipping/removing artifacts (stub for now)
  }

  render(ctx: CanvasRenderingContext2D): void {
    // Will draw two artifact slots (filled or empty)
  }
}
